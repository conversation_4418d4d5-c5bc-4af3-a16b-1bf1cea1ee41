using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using ExamPaperEditingSystem.Models;
using ExamPaperEditingSystem.Services;
using ExamPaperEditingSystem.Utils;

namespace ExamPaperEditingSystem.ViewModels
{
    /// <summary>
    /// UI配置窗口ViewModel
    /// </summary>
    public class UIConfigurationViewModel : INotifyPropertyChanged
    {
        private readonly IConfigurationService _configurationService;
        private readonly Window _window;
        private UIConfiguration _configuration;

        public UIConfigurationViewModel(IConfigurationService configurationService, Window window)
        {
            _configurationService = configurationService;
            _window = window;
            _configuration = UIConfiguration.CreateDefault();

            InitializeCommands();
            LoadConfigurationAsync();
        }

        #region Properties

        public string Theme
        {
            get => _configuration.Theme;
            set
            {
                _configuration.Theme = value;
                OnPropertyChanged();
            }
        }

        public double FontSize
        {
            get => _configuration.FontSize;
            set
            {
                _configuration.FontSize = value;
                OnPropertyChanged();
            }
        }

        public string Language
        {
            get => _configuration.Language;
            set
            {
                _configuration.Language = value;
                OnPropertyChanged();
            }
        }

        public bool ShowStatusBar
        {
            get => _configuration.ShowStatusBar;
            set
            {
                _configuration.ShowStatusBar = value;
                OnPropertyChanged();
            }
        }

        public bool ShowToolbar
        {
            get => _configuration.ShowToolbar;
            set
            {
                _configuration.ShowToolbar = value;
                OnPropertyChanged();
            }
        }

        public bool EnableAnimations
        {
            get => _configuration.EnableAnimations;
            set
            {
                _configuration.EnableAnimations = value;
                OnPropertyChanged();
            }
        }

        public string WindowLayout
        {
            get => _configuration.WindowLayout;
            set
            {
                _configuration.WindowLayout = value;
                OnPropertyChanged();
            }
        }

        #endregion

        #region Commands

        public ICommand ResetDefaultCommand { get; private set; } = null!;
        public ICommand ApplyCommand { get; private set; } = null!;
        public ICommand SaveCommand { get; private set; } = null!;
        public ICommand CancelCommand { get; private set; } = null!;

        private void InitializeCommands()
        {
            ResetDefaultCommand = new RelayCommand(async () => await ResetDefaultAsync());
            ApplyCommand = new RelayCommand(async () => await ApplyAsync());
            SaveCommand = new RelayCommand(async () => await SaveAsync());
            CancelCommand = new RelayCommand(() => _window.DialogResult = false);
        }

        #endregion

        #region Methods

        private async void LoadConfigurationAsync()
        {
            try
            {
                _configuration = await _configurationService.GetUIConfigurationAsync();
                OnPropertyChanged(string.Empty); // 刷新所有属性
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载UI配置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private Task ResetDefaultAsync()
        {
            var result = MessageBox.Show("确定要重置为默认界面设置吗？这将丢失当前所有界面配置。",
                "确认重置", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    _configuration = UIConfiguration.CreateDefault();
                    OnPropertyChanged(string.Empty); // 刷新所有属性
                    MessageBox.Show("界面设置已重置为默认值", "重置成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"重置界面设置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }

            return Task.CompletedTask;
        }

        private async Task ApplyAsync()
        {
            try
            {
                await _configurationService.SaveUIConfigurationAsync(_configuration);
                MessageBox.Show("界面设置已应用", "应用成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用界面设置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task SaveAsync()
        {
            try
            {
                await _configurationService.SaveUIConfigurationAsync(_configuration);
                MessageBox.Show("界面设置保存成功", "保存成功", MessageBoxButton.OK, MessageBoxImage.Information);
                _window.DialogResult = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存界面设置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
