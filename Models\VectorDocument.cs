using System;
using System.Collections.Generic;

namespace ExamPaperEditingSystem.Models
{
    public class VectorDocument
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Content { get; set; } = string.Empty;
        public float[] Embedding { get; set; } = Array.Empty<float>();
        public Dictionary<string, object> Metadata { get; set; } = new();
        public string SourceFile { get; set; } = string.Empty;
        public string KnowledgeBase { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    public class KnowledgeBase
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<VectorDocument> Documents { get; set; } = new();
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }

    public class SearchResult
    {
        public VectorDocument Document { get; set; } = new();
        public float Similarity { get; set; }
        public string RelevantContent { get; set; } = string.Empty;
    }

    public class EmbeddingRequest
    {
        public string Input { get; set; } = string.Empty;
        public string Model { get; set; } = "text-embedding-bge-m3";
    }

    public class EmbeddingResponse
    {
        public List<EmbeddingData> Data { get; set; } = new();
        public string Model { get; set; } = string.Empty;
        public Usage Usage { get; set; } = new();
    }

    public class EmbeddingData
    {
        public string Object { get; set; } = string.Empty;
        public float[] Embedding { get; set; } = Array.Empty<float>();
        public int Index { get; set; }
    }

    public class Usage
    {
        public int PromptTokens { get; set; }
        public int TotalTokens { get; set; }
    }
}