using System.Collections.Generic;
using System.Threading.Tasks;
using ExamPaperEditingSystem.Models;

namespace ExamPaperEditingSystem.Services
{
    public interface IExamPaperService
    {
        Task<ExamPaper> CreateExamPaperAsync(string title, string subject, string grade);
        Task<List<ExamPaper>> GetExamPapersAsync();
        Task<ExamPaper?> GetExamPaperAsync(string id);
        Task SaveExamPaperAsync(ExamPaper examPaper);
        Task DeleteExamPaperAsync(string id);
        Task<string> ExportToWordAsync(ExamPaper examPaper);
        Task<ExamPaper> ImportFromWordAsync(string filePath);
        Task<string> FormatExamPaperForCopyAsync(ExamPaper examPaper);
        Task LoadExamPapersAsync();
        Task<List<ExamPaper>> GetAllExamPapersAsync();
    }
}