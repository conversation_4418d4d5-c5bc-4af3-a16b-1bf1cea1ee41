using System.Collections.Generic;
using System.Threading.Tasks;
using ExamPaperEditingSystem.Models;

namespace ExamPaperEditingSystem.Services
{
    public interface IKnowledgeGraphService
    {
        Task<KnowledgeGraphData> CreateKnowledgeGraphAsync(string name);
        Task<List<KnowledgeGraphData>> GetKnowledgeGraphsAsync();
        Task<KnowledgeGraphData?> GetKnowledgeGraphAsync(string name);
        Task SaveKnowledgeGraphAsync(KnowledgeGraphData graph);
        Task DeleteKnowledgeGraphAsync(string name);
        Task AddNodeAsync(string graphName, KnowledgeNode node);
        Task AddRelationAsync(string graphName, KnowledgeRelation relation);
        Task<List<KnowledgeNode>> SearchNodesAsync(string graphName, string query);
        Task<List<KnowledgeRelation>> GetRelationsAsync(string graphName, string nodeId);
        Task LoadKnowledgeGraphsAsync();
        Task ExtractKnowledgeFromTextAsync(string graphName, string text);
    }
}