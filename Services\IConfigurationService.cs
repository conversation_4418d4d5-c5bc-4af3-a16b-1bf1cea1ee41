using System.Threading.Tasks;
using ExamPaperEditingSystem.Models;

namespace ExamPaperEditingSystem.Services
{
    /// <summary>
    /// 配置服务接口
    /// </summary>
    public interface IConfigurationService
    {
        /// <summary>
        /// 获取AI配置
        /// </summary>
        /// <returns></returns>
        Task<AIConfiguration> GetAIConfigurationAsync();

        /// <summary>
        /// 保存AI配置
        /// </summary>
        /// <param name="configuration"></param>
        /// <returns></returns>
        Task SaveAIConfigurationAsync(AIConfiguration configuration);

        /// <summary>
        /// 重置为默认配置
        /// </summary>
        /// <returns></returns>
        Task ResetToDefaultAsync();

        /// <summary>
        /// 测试API连接
        /// </summary>
        /// <param name="apiUrl"></param>
        /// <param name="isEmbeddingApi"></param>
        /// <returns></returns>
        Task<bool> TestApiConnectionAsync(string apiUrl, bool isEmbeddingApi = false);

        /// <summary>
        /// 获取UI配置
        /// </summary>
        /// <returns></returns>
        Task<UIConfiguration> GetUIConfigurationAsync();

        /// <summary>
        /// 保存UI配置
        /// </summary>
        /// <param name="configuration"></param>
        /// <returns></returns>
        Task SaveUIConfigurationAsync(UIConfiguration configuration);
    }
}
