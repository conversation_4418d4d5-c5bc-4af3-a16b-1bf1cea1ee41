using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;
using ExamPaperEditingSystem.Models;

namespace ExamPaperEditingSystem.Converters
{
    /// <summary>
    /// 布尔值转可见性转换器
    /// </summary>
    public class BooleanToVisibilityConverter : IValueConverter
    {
        public static readonly BooleanToVisibilityConverter Instance = new BooleanToVisibilityConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? Visibility.Visible : Visibility.Collapsed;
            }
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Visibility visibility)
            {
                return visibility == Visibility.Visible;
            }
            return false;
        }
    }

    /// <summary>
    /// 布尔值反转转换器
    /// </summary>
    public class InverseBooleanConverter : IValueConverter
    {
        public static readonly InverseBooleanConverter Instance = new InverseBooleanConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }
            return true;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }
            return false;
        }
    }

    /// <summary>
    /// 角色转背景色转换器
    /// </summary>
    public class RoleToBackgroundConverter : IValueConverter
    {
        public static readonly RoleToBackgroundConverter Instance = new RoleToBackgroundConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string role)
            {
                switch (role.ToLower())
                {
                    case "user":
                        return new SolidColorBrush(Color.FromRgb(227, 242, 253)); // 浅蓝色
                    case "assistant":
                        return new SolidColorBrush(Color.FromRgb(243, 229, 245)); // 浅紫色
                    case "system":
                        return new SolidColorBrush(Color.FromRgb(245, 245, 245)); // 浅灰色
                    default:
                        return new SolidColorBrush(Colors.White);
                }
            }
            return new SolidColorBrush(Colors.White);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 角色转显示名称转换器
    /// </summary>
    public class RoleToDisplayConverter : IValueConverter
    {
        public static readonly RoleToDisplayConverter Instance = new RoleToDisplayConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string role)
            {
                switch (role.ToLower())
                {
                    case "user":
                        return "用户";
                    case "assistant":
                        return "AI助手";
                    case "system":
                        return "系统";
                    default:
                        return role;
                }
            }
            return "未知";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 处理状态转换器
    /// </summary>
    public class ProcessingStatusConverter : IValueConverter
    {
        public static readonly ProcessingStatusConverter Instance = new ProcessingStatusConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isProcessing)
            {
                return isProcessing ? "处理中..." : "就绪";
            }
            return "就绪";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 思维链按钮文本转换器
    /// </summary>
    public class ThinkingButtonTextConverter : IValueConverter
    {
        public static readonly ThinkingButtonTextConverter Instance = new ThinkingButtonTextConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool showThinking)
            {
                return showThinking ? "隐藏思维" : "显示思维";
            }
            return "显示思维";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 布尔值转换器（用于判断对象是否为null）
    /// </summary>
    public class BooleanConverter : IValueConverter
    {
        public static readonly BooleanConverter IsNotNull = new BooleanConverter { TrueValue = true, FalseValue = false, NullValue = false };
        public static readonly BooleanConverter IsNull = new BooleanConverter { TrueValue = false, FalseValue = true, NullValue = true };

        public bool TrueValue { get; set; } = true;
        public bool FalseValue { get; set; } = false;
        public bool NullValue { get; set; } = false;

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null)
                return NullValue;
            
            if (value is bool boolValue)
                return boolValue ? TrueValue : FalseValue;
            
            // 对于非布尔值，检查是否为null
            return TrueValue;
        }

        public object? ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                if (boolValue == TrueValue) return true;
                if (boolValue == FalseValue) return false;
                return null;
            }
            return false;
        }
    }

    /// <summary>
    /// 字符串为空转可见性转换器
    /// </summary>
    public class StringEmptyToVisibilityConverter : IValueConverter
    {
        public static readonly StringEmptyToVisibilityConverter Instance = new StringEmptyToVisibilityConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string str)
            {
                return string.IsNullOrWhiteSpace(str) ? Visibility.Collapsed : Visibility.Visible;
            }
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 集合计数转可见性转换器
    /// </summary>
    public class CountToVisibilityConverter : IValueConverter
    {
        public static readonly CountToVisibilityConverter Instance = new CountToVisibilityConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int count)
            {
                return count > 0 ? Visibility.Visible : Visibility.Collapsed;
            }
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}