D:\AI_project\AI test paper editing system\obj\Debug\net6.0-windows\ExamPaperEditingSystem.csproj.AssemblyReference.cache
D:\AI_project\AI test paper editing system\obj\Debug\net6.0-windows\Styles\Styles.baml
D:\AI_project\AI test paper editing system\obj\Debug\net6.0-windows\Views\MainWindow.g.cs
D:\AI_project\AI test paper editing system\obj\Debug\net6.0-windows\App.g.cs
D:\AI_project\AI test paper editing system\obj\Debug\net6.0-windows\ExamPaperEditingSystem_MarkupCompile.cache
D:\AI_project\AI test paper editing system\obj\Debug\net6.0-windows\ExamPaperEditingSystem_MarkupCompile.lref
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\ExamPaperEditingSystem.exe
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\ExamPaperEditingSystem.deps.json
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\ExamPaperEditingSystem.runtimeconfig.json
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\ExamPaperEditingSystem.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\ExamPaperEditingSystem.pdb
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\BouncyCastle.Cryptography.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\DocumentFormat.OpenXml.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Enums.NET.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\MathNet.Numerics.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.Abstractions.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.Binder.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.CommandLine.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.EnvironmentVariables.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.FileExtensions.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.Json.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.UserSecrets.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.Extensions.DependencyInjection.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.Extensions.DependencyInjection.Abstractions.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.Extensions.FileProviders.Abstractions.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.Extensions.FileProviders.Physical.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.Extensions.FileSystemGlobbing.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.Extensions.Hosting.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.Extensions.Hosting.Abstractions.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.Abstractions.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.Configuration.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.Console.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.Debug.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.EventLog.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.EventSource.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.Extensions.Options.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.Extensions.Options.ConfigurationExtensions.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.Extensions.Primitives.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.IO.RecyclableMemoryStream.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.ML.Core.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.ML.Data.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.ML.KMeansClustering.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.ML.PCA.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.ML.StandardTrainers.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.ML.Transforms.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.ML.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.ML.CpuMath.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.ML.DataView.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Microsoft.ML.OnnxRuntime.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\Newtonsoft.Json.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\NPOI.Core.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\NPOI.OOXML.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\NPOI.OpenXml4Net.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\NPOI.OpenXmlFormats.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\ICSharpCode.SharpZipLib.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\SixLabors.Fonts.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\SixLabors.ImageSharp.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\System.Diagnostics.DiagnosticSource.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\System.Diagnostics.EventLog.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\System.Security.Cryptography.Pkcs.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\System.Security.Cryptography.Xml.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\System.Text.Encodings.Web.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\System.Text.Json.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\linux-arm\native\libLdaNative.so
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\linux-arm64\native\libLdaNative.so
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\linux-x64\native\libLdaNative.so
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\osx-arm64\native\libLdaNative.dylib
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\osx-x64\native\libLdaNative.dylib
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\win-x64\native\LdaNative.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\win-x86\native\LdaNative.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\android\native\onnxruntime.aar
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\ios\native\onnxruntime.xcframework\Info.plist
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\ios\native\onnxruntime.xcframework\ios-arm64\onnxruntime.framework\Headers\coreml_provider_factory.h
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\ios\native\onnxruntime.xcframework\ios-arm64\onnxruntime.framework\Headers\cpu_provider_factory.h
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\ios\native\onnxruntime.xcframework\ios-arm64\onnxruntime.framework\Headers\onnxruntime_c_api.h
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\ios\native\onnxruntime.xcframework\ios-arm64\onnxruntime.framework\Headers\onnxruntime_cxx_api.h
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\ios\native\onnxruntime.xcframework\ios-arm64\onnxruntime.framework\Headers\onnxruntime_cxx_inline.h
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\ios\native\onnxruntime.xcframework\ios-arm64\onnxruntime.framework\Info.plist
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\ios\native\onnxruntime.xcframework\ios-arm64\onnxruntime.framework\onnxruntime
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\ios\native\onnxruntime.xcframework\ios-arm64_x86_64-simulator\onnxruntime.framework\Headers\coreml_provider_factory.h
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\ios\native\onnxruntime.xcframework\ios-arm64_x86_64-simulator\onnxruntime.framework\Headers\cpu_provider_factory.h
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\ios\native\onnxruntime.xcframework\ios-arm64_x86_64-simulator\onnxruntime.framework\Headers\onnxruntime_c_api.h
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\ios\native\onnxruntime.xcframework\ios-arm64_x86_64-simulator\onnxruntime.framework\Headers\onnxruntime_cxx_api.h
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\ios\native\onnxruntime.xcframework\ios-arm64_x86_64-simulator\onnxruntime.framework\Headers\onnxruntime_cxx_inline.h
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\ios\native\onnxruntime.xcframework\ios-arm64_x86_64-simulator\onnxruntime.framework\Info.plist
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\ios\native\onnxruntime.xcframework\ios-arm64_x86_64-simulator\onnxruntime.framework\onnxruntime
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\linux-arm64\native\libonnxruntime.so
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\linux-x64\native\libonnxruntime.so
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\osx.10.14-arm64\native\libonnxruntime.dylib
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\osx.10.14-x64\native\libonnxruntime.dylib
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\win-arm\native\onnxruntime.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\win-arm\native\onnxruntime.lib
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\win-arm\native\onnxruntime_providers_shared.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\win-arm\native\onnxruntime_providers_shared.lib
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\win-arm64\native\onnxruntime.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\win-arm64\native\onnxruntime.lib
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\win-arm64\native\onnxruntime_providers_shared.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\win-arm64\native\onnxruntime_providers_shared.lib
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\win-x64\native\onnxruntime.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\win-x64\native\onnxruntime.lib
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\win-x64\native\onnxruntime_providers_shared.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\win-x64\native\onnxruntime_providers_shared.lib
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\win-x86\native\onnxruntime.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\win-x86\native\onnxruntime.lib
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\win-x86\native\onnxruntime_providers_shared.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\win-x86\native\onnxruntime_providers_shared.lib
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\win\lib\net6.0\System.Diagnostics.EventLog.Messages.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\win\lib\net6.0\System.Diagnostics.EventLog.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\win\lib\net6.0\System.Security.Cryptography.Pkcs.dll
D:\AI_project\AI test paper editing system\bin\Debug\net6.0-windows\runtimes\browser\lib\net6.0\System.Text.Encodings.Web.dll
D:\AI_project\AI test paper editing system\obj\Debug\net6.0-windows\App.baml
D:\AI_project\AI test paper editing system\obj\Debug\net6.0-windows\Views\MainWindow.baml
D:\AI_project\AI test paper editing system\obj\Debug\net6.0-windows\ExamPaperEditingSystem.g.resources
D:\AI_project\AI test paper editing system\obj\Debug\net6.0-windows\ExamPaperEditingSystem.GeneratedMSBuildEditorConfig.editorconfig
D:\AI_project\AI test paper editing system\obj\Debug\net6.0-windows\ExamPaperEditingSystem.AssemblyInfoInputs.cache
D:\AI_project\AI test paper editing system\obj\Debug\net6.0-windows\ExamPaperEditingSystem.AssemblyInfo.cs
D:\AI_project\AI test paper editing system\obj\Debug\net6.0-windows\ExamPaperEditingSystem.csproj.CoreCompileInputs.cache
D:\AI_project\AI test paper editing system\obj\Debug\net6.0-windows\ExamPape.8CBE8D20.Up2Date
D:\AI_project\AI test paper editing system\obj\Debug\net6.0-windows\ExamPaperEditingSystem.dll
D:\AI_project\AI test paper editing system\obj\Debug\net6.0-windows\refint\ExamPaperEditingSystem.dll
D:\AI_project\AI test paper editing system\obj\Debug\net6.0-windows\ExamPaperEditingSystem.pdb
D:\AI_project\AI test paper editing system\obj\Debug\net6.0-windows\ExamPaperEditingSystem.genruntimeconfig.cache
D:\AI_project\AI test paper editing system\obj\Debug\net6.0-windows\ref\ExamPaperEditingSystem.dll
D:\AI_project\AI test paper editing system\obj\Debug\net6.0-windows\Views\AIConfigurationWindow.baml
D:\AI_project\AI test paper editing system\obj\Debug\net6.0-windows\Views\AIConfigurationWindow.g.cs
D:\AI_project\AI test paper editing system\obj\Debug\net6.0-windows\Views\UIConfigurationWindow.baml
D:\AI_project\AI test paper editing system\obj\Debug\net6.0-windows\Views\UIConfigurationWindow.g.cs
