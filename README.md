# AI试卷编辑系统

基于人工智能技术的智能试卷编辑工具，支持文档导入、知识库构建、智能问答生成。

## 🚀 主要功能

### 📚 核心功能
- **文档导入**：支持多种格式的课件文档导入
- **知识库构建**：自动处理文档并建立向量数据库
- **智能试卷生成**：基于知识库内容生成试卷
- **AI对话助手**：与AI助手交流完善试卷内容
- **试卷导出**：将试卷导出为Word文档

### ⚙️ 配置管理
- **AI模型配置**：支持多种AI模型选择和参数调整
- **API设置**：灵活配置在线和本地API端点
- **界面设置**：个性化主题、字体和布局配置
- **代理支持**：支持网络代理配置

## 🛠️ AI配置功能

### 模型配置
- **在线模型**：qwenlong-l1-32b（默认）
- **本地模型**：deepseek-r1-70b（默认）
- **嵌入模型**：text-embedding-bge-m3
- **参数调整**：温度、最大令牌数、超时时间等

### API端点配置
- **在线聊天API**：`http://50844s9656.wocp.fun:42440/v1/chat/completions`
- **在线嵌入API**：`http://50844s9656.wocp.fun:42440/v1/embeddings`
- **本地聊天API**：`http://192.168.100.71:8000/v1/chat/completions`
- **本地嵌入API**：`http://192.168.100.71:8001/v1/embeddings`

### 连接测试
- 支持在线和本地API连接测试
- 实时显示连接状态
- 自动故障转移机制

## 🎨 界面配置

### 主题设置
- 默认主题
- 深色主题
- 浅色主题
- 蓝色主题

### 布局选项
- 标准布局
- 紧凑布局
- 宽屏布局

### 个性化设置
- 字体大小调整（8-24px）
- 工具栏显示/隐藏
- 状态栏显示/隐藏
- 动画效果开关

## 📋 使用说明

### 基本操作流程
1. **启动应用**：运行ExamPaperEditingSystem.exe
2. **配置AI**：通过"设置"菜单配置AI模型和API
3. **导入文档**：选择课件文档创建知识库
4. **创建知识库**：处理文档并建立向量数据库
5. **生成试卷**：基于知识库生成试卷
6. **AI对话**：与AI助手交流完善试卷
7. **导出试卷**：将试卷导出为Word文档

### 配置步骤
1. **打开配置**：点击菜单栏"设置" → "模型配置"或"API设置"
2. **设置参数**：根据需要调整AI模型参数
3. **测试连接**：点击"测试API"按钮验证连接
4. **保存配置**：点击"保存"按钮保存设置

## 🔧 技术架构

### 开发框架
- **.NET 6**：现代化的.NET平台
- **WPF**：Windows桌面应用程序框架
- **MVVM模式**：模型-视图-视图模型架构
- **依赖注入**：Microsoft.Extensions.DependencyInjection

### 核心组件
- **配置服务**：管理AI和UI配置的保存/加载
- **聊天服务**：处理与AI模型的对话交互
- **嵌入服务**：处理文本向量化
- **向量数据库**：存储和检索文档向量
- **知识图谱**：构建文档知识关系

### 数据存储
- **配置文件**：JSON格式存储在用户目录
- **知识库**：向量数据库文件
- **试卷文件**：支持多种格式导出

## 📁 项目结构

```
ExamPaperEditingSystem/
├── Models/                 # 数据模型
│   ├── AIConfiguration.cs  # AI配置模型
│   ├── UIConfiguration.cs  # UI配置模型
│   └── ...
├── Services/               # 业务服务
│   ├── ConfigurationService.cs  # 配置服务
│   ├── ChatService.cs          # 聊天服务
│   └── ...
├── ViewModels/            # 视图模型
│   ├── AIConfigurationViewModel.cs  # AI配置视图模型
│   ├── UIConfigurationViewModel.cs  # UI配置视图模型
│   └── ...
├── Views/                 # 用户界面
│   ├── AIConfigurationWindow.xaml   # AI配置窗口
│   ├── UIConfigurationWindow.xaml   # UI配置窗口
│   └── ...
└── Utils/                 # 工具类
    └── RelayCommand.cs    # 命令实现
```

## 🚨 注意事项

1. **API配置**：首次使用需要配置正确的API端点
2. **网络连接**：确保网络连接正常，特别是使用在线API时
3. **本地部署**：本地API需要相应的AI服务运行
4. **配置备份**：重要配置建议定期备份
5. **版本兼容**：确保AI模型版本与API兼容

## 📞 技术支持

如有问题或建议，请联系：
- 📧 邮箱：<EMAIL>
- 🐛 问题反馈：通过GitHub Issues提交
- 📖 文档：查看项目Wiki获取详细文档

---

**版本**：1.0.0  
**更新日期**：2024年12月  
**开发团队**：AI教育技术团队
