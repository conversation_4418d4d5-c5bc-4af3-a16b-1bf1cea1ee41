using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace ExamPaperEditingSystem.Models
{
    /// <summary>
    /// AI配置模型类
    /// </summary>
    public class AIConfiguration : INotifyPropertyChanged
    {
        private string _chatApiUrl = "http://50844s9656.wocp.fun:42440/v1/chat/completions";
        private string _embeddingApiUrl = "http://50844s9656.wocp.fun:42440/v1/embeddings";
        private string _localChatApiUrl = "http://**************:8000/v1/chat/completions";
        private string _localEmbeddingApiUrl = "http://**************:8001/v1/embeddings";
        private string _chatModel = "qwenlong-l1-32b";
        private string _localChatModel = "deepseek-r1-70b";
        private string _embeddingModel = "text-embedding-bge-m3";
        private double _temperature = 0.7;
        private int _maxTokens = 4000;
        private int _requestTimeout = 300; // 5分钟
        private int _retryCount = 3;
        private bool _useLocalApiFirst = true;
        private bool _enableProxy = false;
        private string _proxyAddress = "";
        private int _proxyPort = 8080;
        private string _proxyUsername = "";
        private string _proxyPassword = "";

        /// <summary>
        /// 在线聊天API地址
        /// </summary>
        public string ChatApiUrl
        {
            get => _chatApiUrl;
            set
            {
                _chatApiUrl = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 在线嵌入向量API地址
        /// </summary>
        public string EmbeddingApiUrl
        {
            get => _embeddingApiUrl;
            set
            {
                _embeddingApiUrl = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 本地聊天API地址
        /// </summary>
        public string LocalChatApiUrl
        {
            get => _localChatApiUrl;
            set
            {
                _localChatApiUrl = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 本地嵌入向量API地址
        /// </summary>
        public string LocalEmbeddingApiUrl
        {
            get => _localEmbeddingApiUrl;
            set
            {
                _localEmbeddingApiUrl = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 在线聊天模型名称
        /// </summary>
        public string ChatModel
        {
            get => _chatModel;
            set
            {
                _chatModel = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 本地聊天模型名称
        /// </summary>
        public string LocalChatModel
        {
            get => _localChatModel;
            set
            {
                _localChatModel = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 嵌入向量模型名称
        /// </summary>
        public string EmbeddingModel
        {
            get => _embeddingModel;
            set
            {
                _embeddingModel = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 响应温度 (0.0-2.0)
        /// </summary>
        public double Temperature
        {
            get => _temperature;
            set
            {
                _temperature = Math.Max(0.0, Math.Min(2.0, value));
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 最大令牌数
        /// </summary>
        public int MaxTokens
        {
            get => _maxTokens;
            set
            {
                _maxTokens = Math.Max(100, Math.Min(32000, value));
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 请求超时时间（秒）
        /// </summary>
        public int RequestTimeout
        {
            get => _requestTimeout;
            set
            {
                _requestTimeout = Math.Max(10, Math.Min(600, value));
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount
        {
            get => _retryCount;
            set
            {
                _retryCount = Math.Max(0, Math.Min(10, value));
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 优先使用本地API
        /// </summary>
        public bool UseLocalApiFirst
        {
            get => _useLocalApiFirst;
            set
            {
                _useLocalApiFirst = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 启用代理
        /// </summary>
        public bool EnableProxy
        {
            get => _enableProxy;
            set
            {
                _enableProxy = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 代理地址
        /// </summary>
        public string ProxyAddress
        {
            get => _proxyAddress;
            set
            {
                _proxyAddress = value ?? "";
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 代理端口
        /// </summary>
        public int ProxyPort
        {
            get => _proxyPort;
            set
            {
                _proxyPort = Math.Max(1, Math.Min(65535, value));
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 代理用户名
        /// </summary>
        public string ProxyUsername
        {
            get => _proxyUsername;
            set
            {
                _proxyUsername = value ?? "";
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 代理密码
        /// </summary>
        public string ProxyPassword
        {
            get => _proxyPassword;
            set
            {
                _proxyPassword = value ?? "";
                OnPropertyChanged();
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 创建默认配置
        /// </summary>
        /// <returns></returns>
        public static AIConfiguration CreateDefault()
        {
            return new AIConfiguration();
        }

        /// <summary>
        /// 复制配置
        /// </summary>
        /// <returns></returns>
        public AIConfiguration Clone()
        {
            return new AIConfiguration
            {
                ChatApiUrl = this.ChatApiUrl,
                EmbeddingApiUrl = this.EmbeddingApiUrl,
                LocalChatApiUrl = this.LocalChatApiUrl,
                LocalEmbeddingApiUrl = this.LocalEmbeddingApiUrl,
                ChatModel = this.ChatModel,
                LocalChatModel = this.LocalChatModel,
                EmbeddingModel = this.EmbeddingModel,
                Temperature = this.Temperature,
                MaxTokens = this.MaxTokens,
                RequestTimeout = this.RequestTimeout,
                RetryCount = this.RetryCount,
                UseLocalApiFirst = this.UseLocalApiFirst,
                EnableProxy = this.EnableProxy,
                ProxyAddress = this.ProxyAddress,
                ProxyPort = this.ProxyPort,
                ProxyUsername = this.ProxyUsername,
                ProxyPassword = this.ProxyPassword
            };
        }
    }
}
