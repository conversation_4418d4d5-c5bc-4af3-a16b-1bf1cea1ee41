using System.Collections.Generic;
using System.Threading.Tasks;
using ExamPaperEditingSystem.Models;

namespace ExamPaperEditingSystem.Services
{
    public interface IDocumentProcessorService
    {
        Task<List<VectorDocument>> ProcessDocumentsAsync(string directoryPath, string knowledgeBaseId);
        Task<List<VectorDocument>> ProcessFileAsync(string filePath, string knowledgeBaseId);
        Task<string> ExtractTextFromFileAsync(string filePath);
        List<string> SplitTextIntoChunks(string text, int maxChunkSize = 1000, int overlap = 200);
        bool IsSupportedFileType(string filePath);
    }
}