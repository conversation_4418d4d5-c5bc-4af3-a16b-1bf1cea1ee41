using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace ExamPaperEditingSystem.Models
{
    public class ChatMessage : INotifyPropertyChanged
    {
        private string _content = string.Empty;
        private string _role = string.Empty;
        private DateTime _timestamp;
        private bool _isThinking;
        private string? _thinkingContent;
        private bool _isThinkingExpanded;

        public string Content
        {
            get => _content;
            set
            {
                _content = value;
                OnPropertyChanged();
            }
        }

        public string Role
        {
            get => _role;
            set
            {
                _role = value;
                OnPropertyChanged();
            }
        }

        public DateTime Timestamp
        {
            get => _timestamp;
            set
            {
                _timestamp = value;
                OnPropertyChanged();
            }
        }

        public bool IsThinking
        {
            get => _isThinking;
            set
            {
                _isThinking = value;
                OnPropertyChanged();
            }
        }

        public string? ThinkingContent
        {
            get => _thinkingContent;
            set
            {
                _thinkingContent = value;
                OnPropertyChanged();
            }
        }

        public bool IsThinkingExpanded
        {
            get => _isThinkingExpanded;
            set
            {
                _isThinkingExpanded = value;
                OnPropertyChanged();
            }
        }

        public bool IsUser => Role == "user";
        public bool IsAssistant => Role == "assistant";

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}