<Window x:Class="ExamPaperEditingSystem.Views.AIConfigurationWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="AI配置设置" Height="700" Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        Icon="../icon.svg">
    
    <Window.Resources>
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,15,0,10"/>
        </Style>
        
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Margin" Value="0,5,0,2"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
        
        <Style x:Key="InputStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="Margin" Value="0,2,0,8"/>
            <Setter Property="BorderBrush" Value="#BDC3C7"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>
        
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Background" Value="#3498DB"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
    </Window.Resources>
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 主要内容区域 -->
        <ScrollViewer Grid.Row="0" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- API端点配置 -->
                <TextBlock Text="API端点配置" Style="{StaticResource SectionHeaderStyle}"/>
                <Border BorderBrush="#ECF0F1" BorderThickness="1" Padding="15" Margin="0,0,0,10">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- 左列 -->
                        <StackPanel Grid.Column="0" Margin="0,0,10,0">
                            <TextBlock Text="在线聊天API地址:" Style="{StaticResource LabelStyle}"/>
                            <TextBox Text="{Binding ChatApiUrl}" Style="{StaticResource InputStyle}"/>
                            
                            <TextBlock Text="在线嵌入向量API地址:" Style="{StaticResource LabelStyle}"/>
                            <TextBox Text="{Binding EmbeddingApiUrl}" Style="{StaticResource InputStyle}"/>
                            
                            <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                <Button Content="测试在线API" Command="{Binding TestOnlineApiCommand}" Style="{StaticResource ButtonStyle}"/>
                                <TextBlock Text="{Binding OnlineApiStatus}" VerticalAlignment="Center" Margin="10,0,0,0"/>
                            </StackPanel>
                        </StackPanel>
                        
                        <!-- 右列 -->
                        <StackPanel Grid.Column="1" Margin="10,0,0,0">
                            <TextBlock Text="本地聊天API地址:" Style="{StaticResource LabelStyle}"/>
                            <TextBox Text="{Binding LocalChatApiUrl}" Style="{StaticResource InputStyle}"/>
                            
                            <TextBlock Text="本地嵌入向量API地址:" Style="{StaticResource LabelStyle}"/>
                            <TextBox Text="{Binding LocalEmbeddingApiUrl}" Style="{StaticResource InputStyle}"/>
                            
                            <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                <Button Content="测试本地API" Command="{Binding TestLocalApiCommand}" Style="{StaticResource ButtonStyle}"/>
                                <TextBlock Text="{Binding LocalApiStatus}" VerticalAlignment="Center" Margin="10,0,0,0"/>
                            </StackPanel>
                        </StackPanel>
                    </Grid>
                </Border>
                
                <!-- 模型配置 -->
                <TextBlock Text="模型配置" Style="{StaticResource SectionHeaderStyle}"/>
                <Border BorderBrush="#ECF0F1" BorderThickness="1" Padding="15" Margin="0,0,0,10">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- 左列 -->
                        <StackPanel Grid.Column="0" Margin="0,0,10,0">
                            <TextBlock Text="在线聊天模型:" Style="{StaticResource LabelStyle}"/>
                            <TextBox Text="{Binding ChatModel}" Style="{StaticResource InputStyle}"/>
                            
                            <TextBlock Text="嵌入向量模型:" Style="{StaticResource LabelStyle}"/>
                            <TextBox Text="{Binding EmbeddingModel}" Style="{StaticResource InputStyle}"/>
                        </StackPanel>
                        
                        <!-- 右列 -->
                        <StackPanel Grid.Column="1" Margin="10,0,0,0">
                            <TextBlock Text="本地聊天模型:" Style="{StaticResource LabelStyle}"/>
                            <TextBox Text="{Binding LocalChatModel}" Style="{StaticResource InputStyle}"/>
                            
                            <CheckBox Content="优先使用本地API" IsChecked="{Binding UseLocalApiFirst}" Margin="0,10,0,0"/>
                        </StackPanel>
                    </Grid>
                </Border>
                
                <!-- 参数配置 -->
                <TextBlock Text="参数配置" Style="{StaticResource SectionHeaderStyle}"/>
                <Border BorderBrush="#ECF0F1" BorderThickness="1" Padding="15" Margin="0,0,0,10">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- 左列 -->
                        <StackPanel Grid.Column="0" Margin="0,0,10,0">
                            <TextBlock Text="响应温度 (0.0-2.0):" Style="{StaticResource LabelStyle}"/>
                            <Slider Value="{Binding Temperature}" Minimum="0" Maximum="2" TickFrequency="0.1" 
                                    IsSnapToTickEnabled="True" Margin="0,2,0,5"/>
                            <TextBlock Text="{Binding Temperature, StringFormat=F1}" HorizontalAlignment="Center" FontSize="10"/>
                            
                            <TextBlock Text="最大令牌数:" Style="{StaticResource LabelStyle}" Margin="0,10,0,2"/>
                            <TextBox Text="{Binding MaxTokens}" Style="{StaticResource InputStyle}"/>
                        </StackPanel>
                        
                        <!-- 右列 -->
                        <StackPanel Grid.Column="1" Margin="10,0,0,0">
                            <TextBlock Text="请求超时时间(秒):" Style="{StaticResource LabelStyle}"/>
                            <TextBox Text="{Binding RequestTimeout}" Style="{StaticResource InputStyle}"/>
                            
                            <TextBlock Text="重试次数:" Style="{StaticResource LabelStyle}"/>
                            <TextBox Text="{Binding RetryCount}" Style="{StaticResource InputStyle}"/>
                        </StackPanel>
                    </Grid>
                </Border>
                
                <!-- 代理配置 -->
                <TextBlock Text="代理配置" Style="{StaticResource SectionHeaderStyle}"/>
                <Border BorderBrush="#ECF0F1" BorderThickness="1" Padding="15" Margin="0,0,0,10">
                    <StackPanel>
                        <CheckBox Content="启用代理" IsChecked="{Binding EnableProxy}" Margin="0,0,0,10"/>
                        
                        <Grid IsEnabled="{Binding EnableProxy}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- 左列 -->
                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <TextBlock Text="代理地址:" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding ProxyAddress}" Style="{StaticResource InputStyle}"/>
                                
                                <TextBlock Text="代理端口:" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding ProxyPort}" Style="{StaticResource InputStyle}"/>
                            </StackPanel>
                            
                            <!-- 右列 -->
                            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                <TextBlock Text="用户名:" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding ProxyUsername}" Style="{StaticResource InputStyle}"/>
                                
                                <TextBlock Text="密码:" Style="{StaticResource LabelStyle}"/>
                                <PasswordBox x:Name="ProxyPasswordBox" Margin="0,2,0,8" Padding="8,6"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
                
            </StackPanel>
        </ScrollViewer>
        
        <!-- 底部按钮 -->
        <Border Grid.Row="1" BorderBrush="#ECF0F1" BorderThickness="0,1,0,0" Padding="0,15,0,0" Margin="0,15,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="重置默认" Command="{Binding ResetDefaultCommand}" Style="{StaticResource ButtonStyle}" Background="#E74C3C"/>
                <Button Content="测试所有连接" Command="{Binding TestAllConnectionsCommand}" Style="{StaticResource ButtonStyle}" Background="#F39C12"/>
                <Button Content="取消" Command="{Binding CancelCommand}" Style="{StaticResource ButtonStyle}" Background="#95A5A6"/>
                <Button Content="保存" Command="{Binding SaveCommand}" Style="{StaticResource ButtonStyle}" Background="#27AE60"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
