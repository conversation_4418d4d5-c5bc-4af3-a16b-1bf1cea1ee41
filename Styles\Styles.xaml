<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <!-- 颜色定义 -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
    <SolidColorBrush x:Key="SecondaryBrush" Color="#F5F5F5"/>
    <SolidColorBrush x:Key="AccentBrush" Color="#FF4081"/>
    <SolidColorBrush x:Key="BorderBrush" Color="#E0E0E0"/>
    <SolidColorBrush x:Key="TextBrush" Color="#212121"/>
    <SolidColorBrush x:Key="LightTextBrush" Color="#757575"/>
    <SolidColorBrush x:Key="BackgroundBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="HoverBrush" Color="#1976D2"/>
    <SolidColorBrush x:Key="UserMessageBrush" Color="#E3F2FD"/>
    <SolidColorBrush x:Key="AssistantMessageBrush" Color="#F3E5F5"/>
    
    <!-- 现代化按钮样式 -->
    <Style x:Key="ModernButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="15,8"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}" 
                            CornerRadius="4" 
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center" 
                                          VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource HoverBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#1565C0"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" Value="#BDBDBD"/>
                            <Setter Property="Foreground" Value="#757575"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 现代化文本框样式 -->
    <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
        <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="10,8"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border Background="{TemplateBinding Background}" 
                            BorderBrush="{TemplateBinding BorderBrush}" 
                            BorderThickness="{TemplateBinding BorderThickness}" 
                            CornerRadius="4">
                        <ScrollViewer x:Name="PART_ContentHost" 
                                      Padding="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                            <Setter Property="BorderThickness" Value="2"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 现代化下拉框样式 -->
    <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox">
        <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="10,8"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ComboBox">
                    <Grid>
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}" 
                                CornerRadius="4">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <ContentPresenter Grid.Column="0" 
                                                  Content="{TemplateBinding SelectionBoxItem}" 
                                                  ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}" 
                                                  Margin="{TemplateBinding Padding}" 
                                                  VerticalAlignment="Center"/>
                                
                                <ToggleButton Grid.Column="1" 
                                              Name="ToggleButton" 
                                              Background="Transparent" 
                                              BorderThickness="0" 
                                              IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" 
                                              ClickMode="Press">
                                    <Path Data="M 0 0 L 4 4 L 8 0 Z" 
                                          Fill="{StaticResource LightTextBrush}" 
                                          HorizontalAlignment="Center" 
                                          VerticalAlignment="Center" 
                                          Margin="8"/>
                                </ToggleButton>
                            </Grid>
                        </Border>
                        
                        <Popup Name="Popup" 
                               Placement="Bottom" 
                               IsOpen="{TemplateBinding IsDropDownOpen}" 
                               AllowsTransparency="True" 
                               Focusable="False" 
                               PopupAnimation="Slide">
                            <Border Background="{StaticResource BackgroundBrush}" 
                                    BorderBrush="{StaticResource BorderBrush}" 
                                    BorderThickness="1" 
                                    CornerRadius="4" 
                                    MaxHeight="200">
                                <ScrollViewer>
                                    <StackPanel IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Contained"/>
                                </ScrollViewer>
                            </Border>
                        </Popup>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                            <Setter Property="BorderThickness" Value="2"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 卡片样式 -->
    <Style x:Key="CardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="15"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="#E0E0E0" 
                                  Direction="270" 
                                  ShadowDepth="2" 
                                  BlurRadius="8" 
                                  Opacity="0.3"/>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 标题样式 -->
    <Style x:Key="TitleStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="24"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{StaticResource TextBrush}"/>
        <Setter Property="Margin" Value="0,0,0,20"/>
    </Style>
    
    <!-- 副标题样式 -->
    <Style x:Key="SubtitleStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{StaticResource TextBrush}"/>
        <Setter Property="Margin" Value="0,0,0,10"/>
    </Style>
    
    <!-- 标签样式 -->
    <Style x:Key="LabelStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="{StaticResource LightTextBrush}"/>
        <Setter Property="Margin" Value="0,0,0,5"/>
    </Style>
    
    <!-- 复选框样式 -->
    <Style TargetType="CheckBox">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="{StaticResource TextBrush}"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
    </Style>
    
    <!-- 滚动条样式 -->
    <Style TargetType="ScrollBar">
        <Setter Property="Background" Value="{StaticResource SecondaryBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Width" Value="12"/>
    </Style>
    
</ResourceDictionary>