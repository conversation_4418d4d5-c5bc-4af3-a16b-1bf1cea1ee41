using System.Windows;
using System.Windows.Controls;
using ExamPaperEditingSystem.Services;
using ExamPaperEditingSystem.ViewModels;

namespace ExamPaperEditingSystem.Views
{
    /// <summary>
    /// AIConfigurationWindow.xaml 的交互逻辑
    /// </summary>
    public partial class AIConfigurationWindow : Window
    {
        private readonly AIConfigurationViewModel _viewModel;

        public AIConfigurationWindow(IConfigurationService configurationService)
        {
            InitializeComponent();
            
            _viewModel = new AIConfigurationViewModel(configurationService, this);
            DataContext = _viewModel;
            
            // 绑定密码框
            ProxyPasswordBox.PasswordChanged += ProxyPasswordBox_PasswordChanged;
            
            // 设置窗口属性
            this.ShowInTaskbar = false;
            this.WindowStartupLocation = WindowStartupLocation.CenterOwner;
        }

        private void ProxyPasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (DataContext is AIConfigurationViewModel viewModel)
            {
                viewModel.ProxyPassword = ((PasswordBox)sender).Password;
            }
        }

        protected override void OnSourceInitialized(System.EventArgs e)
        {
            base.OnSourceInitialized(e);
            
            // 设置密码框的初始值
            if (DataContext is AIConfigurationViewModel viewModel)
            {
                ProxyPasswordBox.Password = viewModel.ProxyPassword;
            }
        }
    }
}
