using System.Windows;
using ExamPaperEditingSystem.Services;
using ExamPaperEditingSystem.ViewModels;

namespace ExamPaperEditingSystem.Views
{
    /// <summary>
    /// UIConfigurationWindow.xaml 的交互逻辑
    /// </summary>
    public partial class UIConfigurationWindow : Window
    {
        private readonly UIConfigurationViewModel _viewModel;

        public UIConfigurationWindow(IConfigurationService configurationService)
        {
            InitializeComponent();
            
            _viewModel = new UIConfigurationViewModel(configurationService, this);
            DataContext = _viewModel;
            
            // 设置窗口属性
            this.ShowInTaskbar = false;
            this.WindowStartupLocation = WindowStartupLocation.CenterOwner;
        }
    }
}
