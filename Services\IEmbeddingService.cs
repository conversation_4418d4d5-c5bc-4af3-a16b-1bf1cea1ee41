using System.Threading.Tasks;
using ExamPaperEditingSystem.Models;

namespace ExamPaperEditingSystem.Services
{
    public interface IEmbeddingService
    {
        Task<float[]> GetEmbeddingAsync(string text, string? apiUrl = null);
        Task<EmbeddingResponse> GetEmbeddingResponseAsync(string text, string? apiUrl = null);
        bool IsOnlineApiAvailable { get; }
        bool IsLocalApiAvailable { get; }
        Task<bool> TestApiConnectionAsync(string apiUrl);
    }
}