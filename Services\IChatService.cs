using System.Collections.Generic;
using System.Threading.Tasks;
using ExamPaperEditingSystem.Models;

namespace ExamPaperEditingSystem.Services
{
    public interface IChatService
    {
        Task<ChatMessage> SendMessageAsync(string message, List<ChatMessage> history, string? apiUrl = null);
        Task<string> StreamMessageAsync(string message, List<ChatMessage> history, string? apiUrl = null);
        bool IsOnlineApiAvailable { get; }
        bool IsLocalApiAvailable { get; }
        Task<bool> TestApiConnectionAsync(string apiUrl);
        string ExtractThinkingContent(string content);
        string RemoveThinkingContent(string content);
    }
}