using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace ExamPaperEditingSystem.Models
{
    public class KnowledgeNode : INotifyPropertyChanged
    {
        private string _id = Guid.NewGuid().ToString();
        private string _label = string.Empty;
        private string _type = string.Empty;
        private Dictionary<string, object> _properties = new();

        public string Id
        {
            get => _id;
            set
            {
                _id = value;
                OnPropertyChanged();
            }
        }

        public string Label
        {
            get => _label;
            set
            {
                _label = value;
                OnPropertyChanged();
            }
        }

        public string Type
        {
            get => _type;
            set
            {
                _type = value;
                OnPropertyChanged();
            }
        }

        public Dictionary<string, object> Properties
        {
            get => _properties;
            set
            {
                _properties = value;
                OnPropertyChanged();
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class KnowledgeRelation : INotifyPropertyChanged
    {
        private string _id = Guid.NewGuid().ToString();
        private string _sourceId = string.Empty;
        private string _targetId = string.Empty;
        private string _type = string.Empty;
        private Dictionary<string, object> _properties = new();

        public string Id
        {
            get => _id;
            set
            {
                _id = value;
                OnPropertyChanged();
            }
        }

        public string SourceId
        {
            get => _sourceId;
            set
            {
                _sourceId = value;
                OnPropertyChanged();
            }
        }

        public string TargetId
        {
            get => _targetId;
            set
            {
                _targetId = value;
                OnPropertyChanged();
            }
        }

        public string Type
        {
            get => _type;
            set
            {
                _type = value;
                OnPropertyChanged();
            }
        }

        public Dictionary<string, object> Properties
        {
            get => _properties;
            set
            {
                _properties = value;
                OnPropertyChanged();
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class KnowledgeGraphData
    {
        public List<KnowledgeNode> Nodes { get; set; } = new();
        public List<KnowledgeRelation> Relations { get; set; } = new();
        public string Name { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }
}