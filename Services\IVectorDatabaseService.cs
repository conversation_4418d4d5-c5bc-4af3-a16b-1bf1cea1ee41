using System.Collections.Generic;
using System.Threading.Tasks;
using ExamPaperEditingSystem.Models;

namespace ExamPaperEditingSystem.Services
{
    public interface IVectorDatabaseService
    {
        Task<string> CreateKnowledgeBaseAsync(string name, string description);
        Task<List<KnowledgeBase>> GetKnowledgeBasesAsync();
        Task<KnowledgeBase?> GetKnowledgeBaseAsync(string id);
        Task DeleteKnowledgeBaseAsync(string id);
        Task AddDocumentAsync(string knowledgeBaseId, VectorDocument document);
        Task<List<VectorDocument>> GetDocumentsAsync(string knowledgeBaseId);
        Task<List<SearchResult>> SearchAsync(string knowledgeBaseId, string query, int topK = 5);
        Task<List<SearchResult>> SearchAllAsync(string query, int topK = 5);
        Task SaveKnowledgeBaseAsync(KnowledgeBase knowledgeBase);
        Task LoadKnowledgeBasesAsync();
    }
}